<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Controller\Backend;

use Bgs\LandingPages\Domain\Repository\FlightRouteRepository;
use Bgs\LandingPages\Service\VirtualRouteService;
use Psr\Http\Message\ResponseInterface;
use TYPO3\CMS\Backend\Template\ModuleTemplateFactory;
use TYPO3\CMS\Core\Page\PageRenderer;
use TYPO3\CMS\Extbase\Mvc\Controller\ActionController;

/**
 * Backend Module Controller for Landing Pages
 */
class LandingPagesModuleController extends ActionController
{
    public function __construct(
        private readonly ModuleTemplateFactory $moduleTemplateFactory,
        private readonly FlightRouteRepository $flightRouteRepository,
        private readonly VirtualRouteService $virtualRouteService,
        private readonly PageRenderer $pageRenderer
    ) {
    }



    /**
     * Main module index action
     */
    public function indexAction(): ResponseInterface
    {
        $moduleTemplate = $this->moduleTemplateFactory->create($this->request);
        $moduleTemplate->setTitle('Landing Pages');

        // Add some basic statistics
        $statistics = $this->getStatistics();

        $this->view->assignMultiple([
            'statistics' => $statistics,
            'extensionVersion' => $this->getExtensionVersion(),
        ]);

        $moduleTemplate->setContent($this->view->render());
        return $this->htmlResponse($moduleTemplate->renderContent());
    }

    /**
     * Overview action showing detailed information
     */
    public function overviewAction(): ResponseInterface
    {
        $moduleTemplate = $this->moduleTemplateFactory->create($this->request);
        $moduleTemplate->setTitle('Landing Pages - Overview');

        // Get detailed information
        $flightRoutes = $this->flightRouteRepository->findAll();
        $statistics = $this->getStatistics();

        $this->view->assignMultiple([
            'flightRoutes' => $flightRoutes,
            'statistics' => $statistics,
            'extensionVersion' => $this->getExtensionVersion(),
        ]);

        $moduleTemplate->setContent($this->view->render());
        return $this->htmlResponse($moduleTemplate->renderContent());
    }

    /**
     * Get basic statistics for the module
     */
    private function getStatistics(): array
    {
        $totalRoutes = $this->flightRouteRepository->countAll();
        $activeRoutes = $this->flightRouteRepository->countByActive(true);
        
        return [
            'totalRoutes' => $totalRoutes,
            'activeRoutes' => $activeRoutes,
            'inactiveRoutes' => $totalRoutes - $activeRoutes,
        ];
    }

    /**
     * Get extension version from ext_emconf.php
     */
    private function getExtensionVersion(): string
    {
        $extEmconfPath = \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::extPath('landing-pages') . 'ext_emconf.php';
        if (file_exists($extEmconfPath)) {
            $EM_CONF = [];
            include $extEmconfPath;
            return $EM_CONF['landing-pages']['version'] ?? '1.0.0';
        }
        return '1.0.0';
    }
}
