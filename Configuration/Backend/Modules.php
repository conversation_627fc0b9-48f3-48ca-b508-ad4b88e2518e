<?php

/**
 * Backend module configuration for Landing Pages extension
 */
return [
    'web_landingpages' => [
        'parent' => 'web',
        'position' => ['after' => 'web_info'],
        'access' => 'user',
        'workspaces' => 'live',
        'path' => '/module/web/landingpages',
        'labels' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_mod.xlf',
        'extensionName' => 'LandingPages',
        'controllerActions' => [
            \Bgs\LandingPages\Controller\Backend\LandingPagesModuleController::class => [
                'index',
                'overview',
            ],
        ],
    ],
];
